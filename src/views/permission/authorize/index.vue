<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-left">
      <BasicLeftTree title="业务域" :showToolbar="false" :treeData="treeData" :loading="treeLoading" @reload="reloadTree" @select="handleTreeSelect" />
    </div>
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable" :searchInfo="searchInfo" ref="tableRef">
          <template #tableTitle>
            <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="addOrUpdateHandle()">{{ t('common.addText') }}</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'enabledMark'">
              <a-tag :color="record.enabledMark == 1 ? 'success' : 'error'">{{ record.enabledMark == 1 ? '启用' : '禁用' }}</a-tag>
            </template>
            <template v-if="column.key === 'userCount'">
              <a v-if="record.userCount > 0" @click="openUserListModule(true, { name: record.fullName, id: record.id, total: record.userCount })">{{
                record.userCount || 0
              }}</a>
              <span v-else>0</span>
            </template>

            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" :dropDownActions="getDropDownActions(record)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <Form @register="registerForm" @reload="reload" />
    <AuthorizePopup @register="registerAuthorize" @reload="reload" />
    <PortalOrFlowAuthorizePopup @register="registerPortalOrFlowAuthorize" @reload="reload" />
    <UsersSelectModal @register="registerUsersSelect" :getDataApi="getPermissionMember" :saveDataApi="savePermissionMember" @initData="reload" />
  </div>
  <userListModule @register="registerUserListModule" @reload="reload" />
</template>

<script lang="ts" setup>
  import { ref, reactive, defineOptions, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction, BasicColumn, ActionItem } from '@/components/Table';
  import { getList, del, copy, getPermissionMember, savePermissionMember } from '@/api/permission/permissionGroup';
  import { usePopup } from '@/components/Popup';
  import { useModal } from '@/components/Modal';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';
  import Form from './Form.vue';
  import AuthorizePopup from './components/AuthorizePopup.vue';
  import PortalOrFlowAuthorizePopup from './components/PortalOrFlowAuthorizePopup.vue';
  import UsersSelectModal from './components/UsersSelectModal.vue';
  import { BasicLeftTree, TreeItem } from '@/components/Tree';
  import { useBaseStore } from '@/store/modules/base';
  import userListModule from '@/views/permission/authorize/userListModule.vue';
  import { useDrawer } from '@/components/Drawer';

  defineOptions({ name: 'permission-authorize' });

  const { t } = useI18n();
  const baseStore = useBaseStore();
  const { createMessage } = useMessage();
  const [registerUserListModule, { openDrawer: openUserListModule }] = useDrawer();

  const columns: BasicColumn[] = [
    { title: '权限组名称', dataIndex: 'fullName', width: 200 },
    { title: '权限组编码', dataIndex: 'enCode', width: 200 },
    // { title: '权限组类型', dataIndex: 'type', width: 90 },
    { title: '数据来源', dataIndex: 'dataSourceFullname' },
    { title: '操作权限', dataIndex: 'operationPermission' },
    { title: '说明', dataIndex: 'description' },
    { title: '权限组成员', dataIndex: 'member', width: 150, customRender: ({ record }) => (record.type == 0 ? '全部成员' : '自定义') },
    {
      title: '用户组类型',
      dataIndex: 'type',
      width: 100,
      customRender: ({ record }) => {
        return record.type ? '动态' : '固定';
      },
    },
    { title: '用户数', dataIndex: 'userCount', align: 'center', width: 100 },
    { title: '创建时间', dataIndex: 'creatorTime', width: 150, format: 'date|YYYY-MM-DD HH:mm:ss' },
    { title: '排序', dataIndex: 'sortCode', width: 70, align: 'center' },
    { title: '状态', dataIndex: 'enabledMark', width: 70, align: 'center' },
  ];
  const searchInfo = reactive({
    domainId: '',
  });
  const treeLoading = ref(false);
  const treeData = ref<TreeItem[]>([]);
  const organizeIdTree = ref([]);
  const businessDomains = ref([]);
  const [registerTable, { reload, setLoading, getForm }] = useTable({
    api: getList,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          componentProps: {
            placeholder: t('common.enterKeyword'),
            submitOnPressEnter: true,
          },
        },
        {
          field: 'enabledMark',
          label: '状态',
          component: 'Select',
          componentProps: {
            placeholder: '请选择',
            options: [
              { fullName: '启用', id: 1 },
              { fullName: '禁用', id: 0 },
            ],
          },
        },
      ],
    },
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
    },
  });
  const [registerForm, { openModal: openFormModal }] = useModal();
  const [registerAuthorize, { openPopup: openAuthorizePopup }] = usePopup();
  const [registerPortalOrFlowAuthorize, { openPopup: openPortalOrFlowAuthorizePopup }] = usePopup();
  const [registerUsersSelect, { openModal: registerUsersSelectModal }] = useModal();

  function getTableActions(record): ActionItem[] {
    return [
      {
        label: t('common.editText'),
        disabled: record.dataSource == 0,
        onClick: addOrUpdateHandle.bind(null, record.id),
      },
      {
        label: t('common.delText'),
        disabled: record.dataSource == 0,
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
    ];
  }
  function getDropDownActions(record): ActionItem[] {
    return [
      {
        ifShow: record.type == 0,
        label: '权限成员',
        onClick: handleMember.bind(null, record.id),
      },
      {
        label: '权限授权',
        onClick: handleAuthorize.bind(null, record),
      },
      {
        label: '首页授权',
        onClick: handlePortalOrFlowAuthorize.bind(null, record, 'portal'),
        disabled: record.dataSource != 1,
      },
      {
        label: '流程授权',
        onClick: handlePortalOrFlowAuthorize.bind(null, record, 'flow'),
        disabled: record.dataSource != 1,
      },
      {
        label: '用户组复制',
        modelConfirm: {
          content: '您确定要复制该用户组, 是否继续?',
          onOk: handleCopy.bind(null, record.id),
        },
        disabled: record.dataSource != 1,
      },
    ];
  }
  async function initData(isInit = false) {
    treeLoading.value = true;
    if (isInit) setLoading(true);
    const res = await baseStore.getDictionaryData('businessDomains');
    treeData.value = res;
    businessDomains.value = res;
    treeLoading.value = false;
    reload({ page: 1 });
  }
  function reloadTree() {
    console.log(234234);
    treeData.value = [];
    initData();
  }
  function handleTreeSelect(id, _node, nodePath) {
    if (!id || searchInfo.domainId === id) return;
    searchInfo.domainId = id;
    organizeIdTree.value = nodePath.map(o => o.id);
    getForm().resetFields();
  }
  function addOrUpdateHandle(id = '') {
    openFormModal(true, { id });
  }
  function handleDelete(id) {
    del(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }
  function handleMember(id) {
    registerUsersSelectModal(true, { id });
  }
  function handleAuthorize(record) {
    openAuthorizePopup(true, { id: record.id, fullName: record.fullName + '的权限授权', type: 'Batch' });
  }
  function handlePortalOrFlowAuthorize(record, type) {
    const fullName = `${record.fullName}的${type == 'portal' ? '门户' : '流程'}权限`;
    openPortalOrFlowAuthorizePopup(true, { id: record.id, fullName, type });
  }
  function handleCopy(id) {
    copy(id).then(res => {
      createMessage.success(res.msg);
      reload();
    });
  }

  onMounted(() => {
    initData(true);
  });
</script>
