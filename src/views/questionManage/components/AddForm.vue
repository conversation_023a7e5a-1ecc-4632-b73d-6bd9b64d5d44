<!--
 * @Description: 创建问卷
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-31 13:57:24
-->
<template>
  <a-row class="mb-100px">
    <a-col :span="14" :offset="5">
      <BasicForm @register="registerForm"> </BasicForm>
    </a-col>
  </a-row>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { BasicForm, useForm, FormSchema } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  const id = ref('');
  const schemas: FormSchema[] = [
    {
      field: 'surveyName',
      label: '问卷名称',
      component: 'Input',
      rules: [{ required: true, message: '问卷名称不能为空' }],
    },
    {
      field: 'totalScore',
      label: '总分数',
      component: 'InputNumber',
      helpMessage: '问卷可得最大分值，存在使用分值题目有效',
      rules: [{ required: true, message: '总分数不能为空' }],
    },
    {
      field: 'description',
      label: '填写说明',
      component: 'Textarea',
      componentProps: {
        maxLength: 1000,
        placeholder: '请输入填写说明',
      },
    },
    {
      field: 'startTime',
      label: '填写开始时间',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
      },
      rules: [{ required: true, message: '请选择填写开始时间' }],
    },
    {
      field: 'endTime',
      label: '填写结束时间',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
      },
      rules: [{ required: true, message: '请选择填写结束时间' }],
    },
    {
      field: 'usageScope',
      label: '使用范围',
      component: 'Select',
      componentProps: {
        options: [{ fullName: '困难生', value: '困难生' }],
      },
      rules: [{ required: true, message: '请选择使用范围' }],
    },
    {
      field: 'usagery',
      label: '调查对象',
      component: 'Radio',
      componentProps: {
        options: [
          { fullName: '全体学生', id: '1' },
          { fullName: '部分学生', id: '2' },
        ],
        onChange: e => {
          emit('judgeShowStuList', e === '2' ? true : false);
        },
      },

      rules: [{ required: true, message: '请选择使用范围' }],
    },
    {
      field: 'isUsed',
      label: '是否使用',
      component: 'Switch',
      defaultValue: true,
    },
    {
      field: 'isMultiple',
      label: '是否多次填写',
      component: 'Switch',
    },
  ];
  const emit = defineEmits(['register', 'reload']);
  const { createMessage } = useMessage();
  const [registerForm, { setFieldsValue, getFieldsValue, validate, resetFields, updateSchema }] = useForm({
    labelWidth: 100,
    schemas: schemas,
  });
  function save() {
    const generatedId = 'new-generated-id';
    return {
      success: true,
      id: generatedId,
      errorMessage: '',
    };
  }
  function getDetail() {}
  defineExpose({
    save,
    getDetail,
  });
  onMounted(() => {
    resetFields();
  });
</script>
<style scoped lang="less">
  :deep(.ant-alert-with-description) {
    padding-block: 5px !important;
  }
  :deep(.ant-descriptions .ant-descriptions-item-label) {
    padding: 10px !important;
    width: 130px !important;
  }
</style>
