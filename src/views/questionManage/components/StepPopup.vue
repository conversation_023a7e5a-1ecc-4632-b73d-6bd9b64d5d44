<template>
  <BasicPopup
    v-bind="$attrs"
    :title="state.title"
    :showOkBtn="state.activeStep >= state.maxStep && state.shareInfo.operationType != 2"
    cancelText="关闭"
    @register="registerPopup"
    class="full-popup"
    @ok="handleSubmit"
    @close="handleClose">
    <template #insertToolbar>
      <a-space :size="10">
        <a-button @click="handlePrev()" :disabled="state.activeStep <= 0 || state.loading">上一步</a-button>
        <a-button @click="handleNext()" :disabled="state.activeStep >= state.maxStep || state.loading">下一步</a-button>
      </a-space>
    </template>
    <ScrollContainer>
      <div class="steps">
        <a-steps :current="state.activeStep" :items="stepItems.steps" @change="changeSteps"></a-steps>
      </div>
      <component :is="currentStepComponent" :stepIds="state.stepIds" :stepItems="stepItems" :shareInfo="state.shareInfo" ref="currentComponentRef" />
    </ScrollContainer>
  </BasicPopup>
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, toRefs, nextTick, watch, unref } from 'vue';
  import { BasicPopup, usePopup, usePopupInner } from '@/components/Popup';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { ScrollContainer } from '@/components/Container';
  import AddForm from './AddForm.vue';
  import CheckedStuList from './CheckedStuList.vue';
  import QuestionManage from './QuestionManage.vue';

  const emit = defineEmits(['register', 'reload', 'update']);

  const { createMessage } = useMessage();
  const { t } = useI18n();
  const stepItems = reactive({
    steps: [
      { title: '基础信息', com: 'AddForm' },
      { title: '学生名单', com: 'CheckedStuList' },
      { title: '问卷题目', com: 'QuestionManage' },
    ],
  });
  const state = reactive({
    title: '',
    loading: false,
    activeStep: 0,
    maxStep: stepItems.steps.length - 1,
    initIds: '', // 初始化id
    stepIds: new Array(stepItems.steps.length).fill(''),
    shareInfo: { operationType: 0 }, //0 新增  1 编辑 2 查看
  });

  // 当前步骤组件，动态获取
  const currentStepComponent = computed(() => {
    const componentName = stepItems.steps[state.activeStep]?.com;
    if (!componentName) return null;

    const components = {
      AddForm,
      CheckedStuList,
      QuestionManage,
    };
    return components[componentName] || null;
  });

  // 给动态组件添加 ref
  const currentComponentRef = ref(null);

  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);

  async function init(data) {
    state.initIds = data.id;
    state.stepIds[0] = data.id || '';
    state.shareInfo.operationType = data.operationType || 2;
    state.activeStep = 0;
    state.title = `${{ 0: '新增', 1: '编辑', 2: '详情' }[Number(data.operationType)]}`;
    nextTick(async () => {
      const currentComponent = unref(currentComponentRef);
      await currentComponent?.getDetail?.();
    });
    state.loading = false;
  }

  const handleSubmit = async () => {
    state.loading = true;
    const currentComponent = unref(currentComponentRef);
    const result = await currentComponent?.save?.();
    if (!result || !result.success) {
      if (result?.errorMessage) createMessage.error(result?.errorMessage);
      state.loading = false;
      return;
    } else {
      createMessage.success('保存成功');
      handleClose();
    }
  };

  const changeSteps = val => {
    if (state.activeStep === val) return;
    if (val > state.activeStep) {
      handleNext(val);
    } else {
      handlePrev(val);
    }
  };

  const handleNext = async (stepNum?: number) => {
    state.loading = true;
    const currentComponent = unref(currentComponentRef);
    if (!currentComponent) {
      createMessage.error('当前步骤组件未定义');
      state.loading = false;
      return;
    }

    try {
      if (state.shareInfo.operationType != 2) {
        const result = await currentComponent.save?.();
        if (!result || !result.success) {
          if (result?.errorMessage) createMessage.error(result?.errorMessage);
          state.loading = false;
          return;
        }
        state.stepIds[state.activeStep] = result.id;
        if (state.activeStep == 0) state.initIds = result.id;
      }

      state.activeStep = stepNum ?? state.activeStep + 1;
      nextTick(async () => {
        const nextComponent = unref(currentComponentRef);
        await nextComponent?.getDetail?.();
      });
    } catch (error) {
      createMessage.error('保存失败，请检查表单内容');
    } finally {
      state.loading = false;
    }
  };

  const handlePrev = async (stepNum?: number) => {
    state.activeStep = stepNum ?? state.activeStep - 1;
    nextTick(async () => {
      const prevComponent = unref(currentComponentRef);
      await prevComponent?.getDetail?.();
    });
  };
  const handleClose = () => {
    closePopup();
    emit('reload');
  };
  watch(
    () => stepItems.steps,
    val => {
      state.maxStep = val.length - 1;
      state.stepIds = new Array(val.length).fill('');
      state.stepIds[0] = state.initIds || '';
    },
    { immediate: true, deep: true },
  );
</script>
<style lang="less" scoped>
  .steps {
    padding: 20px 10%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: sticky;
    top: 0;
    background: white;
    z-index: 2;
  }
</style>
